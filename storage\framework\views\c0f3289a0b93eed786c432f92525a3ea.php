<?php $__env->startSection("wrapper"); ?>
<div class="pagination-list d-flex w-100">
    <a> / blog / comments</a>
</div>
<div class="content-section-box">

    <div class="datatable_parent">

        <table id="blog_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Status</th>
                    <th style="min-width: 100px;">Name</th>
                    <th style="min-width: 100px;">Email</th>
                    <th style="min-width: 250px;">Message</th>
                    <th style="min-width: 250px;">Blog</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $blogCommentList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blogCommentList): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="comment_row">
                    <td></td>
                    <td>
                        <label class="toogle_switch mb-3">
                            <input type="checkbox" class="blog_comment_switch_input"
                                <?php echo e($blogCommentList->status==1 ? "checked" : ""); ?> data-id="<?php echo e($blogCommentList->id); ?>">
                            <span class="agent_switch switch round" data-id="<?php echo e($blogCommentList->id); ?>"></span>
                        </label>
                    </td>
                    <td><?php echo e($blogCommentList->name); ?></td>
                    <td><?php echo e($blogCommentList->email); ?></td>
                    <td><?php echo e($blogCommentList->message); ?></td>
                    <td><?php echo e($blogCommentList->blog->title); ?></td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="<?php echo e($blogCommentList->id); ?>" type="button"
                                    class="delete_btn delete_blog_comment"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                            </div>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

</div>

<?php echo $__env->make("dashboard.blogs.deleteComment", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make("dashboard.include.layout", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/blogs/commentListing.blade.php ENDPATH**/ ?>